# accounts/views.py
from uuid import uuid4
from django.views import View
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import  status
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
from accounts.models import CustomUser, EmergencyContact, EmergencyMedical
from accounts.serializer import (
    EmailChangeSerializer,
    RegisterSerializer,
    LoginSerializer,
    PasswordResetSerializer,
    PasswordResetConfirmSerializer,
    VerifyOTPSerializer,
    InvitationRegisterSerializer,
)
from rest_framework.permissions import IsAuthenticated
from rest_framework.permissions import AllowAny

from django.shortcuts import redirect, render
from allauth.socialaccount.providers.oauth2.views import OAuth2LoginView
from django.urls import reverse
from django.http import Http404
from allauth.socialaccount.providers.google.views import GoogleOAuth2Adapter
from allauth.socialaccount.models import SocialAccount
from rest_framework import serializers
from django.core.mail import send_mail
from django.conf import settings
from django.urls import reverse
from accounts.models import Temporary<PERSON>ser, CustomUser, PendingEmailChange
import logging
from django.contrib.auth import authenticate
from django.contrib.auth import get_user_model
from django.contrib.auth.tokens import default_token_generator
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode
from django.utils.encoding import force_bytes
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from rest_framework.renderers import JSONRenderer
from allauth.socialaccount.providers.apple.views import AppleOAuth2Adapter
from django.contrib.auth import login
from django.utils import timezone
from django.db import transaction
from django.core.exceptions import ValidationError
from django.core.validators import validate_email
from rest_framework import viewsets
from rest_framework.decorators import api_view, permission_classes
from .helpers import validate_custom_url_username, generate_custom_url_suggestions, get_user_by_identifier_or_404

logger = logging.getLogger(__name__)
from django.db import transaction
PROVIDER_ADAPTER_MAPPING = {
    'google': GoogleOAuth2Adapter,
    'apple': AppleOAuth2Adapter,
    # Add other providers here
}


def get_provider_adapter(provider_name):
    adapter_class = PROVIDER_ADAPTER_MAPPING.get(provider_name)
    if adapter_class:
        return adapter_class
    else:
        raise ValueError(f"Provider {provider_name} is not supported.")

class CustomOAuthLogin(OAuth2LoginView):
    def dispatch(self, request, *args, **kwargs):
        provider_name = kwargs.get('provider')

        # Dynamically retrieve the provider adapter
        provider_adapter = get_provider_adapter(provider_name)

        if not provider_adapter:
            raise Http404("Provider not supported")

        # Redirect to the provider-specific login
        self.adapter_class = provider_adapter
        self.adapter = provider_adapter(request)

        return super().dispatch(request, *args, **kwargs)




def get_tokens_for_user(user):
    refresh = RefreshToken.for_user(user)
    return {
        'refresh': str(refresh),
        'access': str(refresh.access_token),
    }

@method_decorator(login_required, name='dispatch')
class CustomOAuthCallback(View):
    def get(self, request, *args, **kwargs):
        user = request.user

        # Generate JWT tokens for the authenticated user
        tokens = get_tokens_for_user(user)
        # Get user email and name
        user_email = user.email
        user_full_name = user.name
        user_first_name = user.first_name
        user_last_name = user.last_name
        user_id = user.id
        profile_image_url = None
        # Check if the user logged in through Google
        social_account = SocialAccount.objects.filter(user=user).first()
        
        if social_account:
            if social_account.provider == 'google':
                profile_image_url = self._handle_google_profile_image(user, social_account)
            elif social_account.provider == 'apple':
                # Apple doesn't provide a profile image, so we'll leave it as None
                pass
        
        
        profile_image_url = self._handle_google_profile_image(user, social_account)
        parameters_in_url = f'access_token={tokens["access"]}&' \
                            f'refresh_token={tokens["refresh"]}&' \
                            f'email={user_email}' \
                            f'&name={user_full_name}' \
                            f'&first_name={user_first_name}' \
                            f'&last_name={user_last_name}' \
                            f'&user_id={user_id}' \
                            f'&profile_image_url={profile_image_url}'

        return redirect(f'/sso/?{parameters_in_url}')

    def _handle_google_profile_image(self, user, social_account):
        if social_account and social_account.provider == 'google':
            # Extract the profile image from extra_data
            profile_image_url = social_account.extra_data.get('picture', None)

            if profile_image_url:
                user.profile_image = profile_image_url
                print(f"Google profile image URL: {profile_image_url}")
                return profile_image_url


from rest_framework.renderers import JSONRenderer

class RegisterAPI(APIView):
    permission_classes = [AllowAny]
    renderer_classes = [JSONRenderer]  # Ensure JSON rendering

    def post(self, request):
        serializer = RegisterSerializer(data=request.data)
        if serializer.is_valid():
            temp_user = serializer.save()
            response_data = {
                "status": 201,
                "message": 'Verification email sent to the email address you provided. Please check your inbox.',
                'email': temp_user.email,
                'is_clinic_signup': temp_user.is_clinic_signup,
                'is_enterprise_signup': temp_user.is_enterprise_signup
            }
            return Response(response_data, status=status.HTTP_201_CREATED)

        return Response({"status": 400, "errors": serializer.errors}, status=status.HTTP_400_BAD_REQUEST)

class InvitationRegisterAPI(APIView):
    """
    API endpoint for invitation-based user registration
    Requires a valid invitation code to register
    """
    permission_classes = [AllowAny]
    renderer_classes = [JSONRenderer]

    def post(self, request):
        """
        Register a new user with invitation code
        """
        try:
            serializer = InvitationRegisterSerializer(data=request.data)
            if serializer.is_valid():
                temp_user = serializer.save()
                
                # Get the used invitation code for response
                invitation_code = request.data.get('invitation_code', '').strip().upper()
                
                response_data = {
                    "status": 201,
                    "message": 'Invitation code accepted. Verification email sent to the email address you provided. Please check your inbox.',
                    'email': temp_user.email,
                    'invitation_code': invitation_code,
                    'is_clinic_signup': temp_user.is_clinic_signup,
                    'is_enterprise_signup': temp_user.is_enterprise_signup,
                    'is_mobile_signup': temp_user.is_mobile_signup
                }
                
                logger.info(f"Invitation-based registration successful for {temp_user.email} with code {invitation_code}")
                return Response(response_data, status=status.HTTP_201_CREATED)
            else:
                logger.warning(f"Invitation-based registration failed: {serializer.errors}")
                return Response(
                    {"status": 400, "errors": serializer.errors}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
                
        except Exception as e:
            logger.error(f"Unexpected error in invitation registration: {str(e)}")
            return Response(
                {
                    "status": 500, 
                    "error": "Internal server error during registration"
                }, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )



from roles.models import Role

#  class VerifyEmailAPI(APIView):
#     permission_classes = [AllowAny]

#     def get(self, request, token):
#         logger.info(f"Attempting to verify email with token: {token}")
#         try:
#             temp_user = TemporaryUser.objects.get(verification_token=token)
#             logger.info(f"Found temporary user: {temp_user.email}")
            
#             User = get_user_model()
#             existing_user = User.objects.filter(email=temp_user.email).first()
            
#             if existing_user:
#                 if existing_user.is_email_verified:
#                     logger.info(f"Email {temp_user.email} is already verified.")
#                     return render(request, 'account/already_verified.html', {
#                         'message': 'This email is already verified.'
#                     })
#                 else:
#                     existing_user.is_active = True
#                     existing_user.is_email_verified = True
#                     # existing_user.name = temp_user.name
#                     # existing_user.assign_role(temp_user.role)
#                     existing_user.save()
#                     user = existing_user
#                     logger.info(f"Updated existing user: {user.email}")
#             else:
#                 user = User.objects.create_user(
#                     email=temp_user.email,
#                     password=temp_user.password,
#                     # name=temp_user.name,
#                     is_active=True,
#                     is_email_verified=True
#                 )
#                 # user.assign_role(temp_user.role)
#                 logger.info(f"Created new user: {user.email}")
            
#             temp_user.delete()
#             logger.info(f"Deleted temporary user: {temp_user.email}")
            
#             refresh = RefreshToken.for_user(user)
#             return render(request, 'account/verify_success.html', {
#                 # 'user_name': user.name,
#                 'user_email': user.email,
#                 # "role": user.role.name if user.role else None,
#                 'refresh_token': str(refresh),
#                 'access_token': str(refresh.access_token),
#             })
#         except TemporaryUser.DoesNotExist:
#             logger.error(f"No temporary user found with token: {token}")
#             # Check if there's a verified user with this email
#             email = request.GET.get('email')  # Assuming email is passed as a query parameter
#             if email:
#                 user = User.objects.filter(email=email, is_email_verified=True).first()
#                 if user:
#                     return render(request, 'account/already_verified.html', {
#                         'message': 'This email is already verified.'
#                     })
                    
#             return render(request, 'account/already_verified.html', {
#                 'message': 'Invalid verification token'
#             })
#         except Exception as e:
#             logger.exception(f"Unexpected error during email verification: {str(e)}")
#             return render(request, 'account/verify_failed.html', {
#                 'message': 'An unexpected error occurred'
#             })



class VerifyEmailAPI(APIView):
    permission_classes = [AllowAny]
    def get(self, request, token):
        logger.info(f"Attempting to verify email with token: {token}")
        try:
            temp_user = TemporaryUser.objects.get(verification_token=token)
            logger.info(f"Found temporary user: {temp_user.email}")

            User = get_user_model()
            existing_user = User.objects.filter(email=temp_user.email).first()

            if existing_user:
                if existing_user.is_email_verified:
                    logger.info(f"Email {temp_user.email} is already verified.")
                    return render(request, 'account/already_verified.html', {
                        'message': 'This email is already verified.'
                    })
                else:
                    existing_user.is_active = True
                    existing_user.is_email_verified = True
                    # existing_user.name = temp_user.name
                    # existing_user.assign_role(temp_user.role)
                    existing_user.save()
                    user = existing_user
                    logger.info(f"Updated existing user: {user.email}")
            else:
                user = User.objects.create_user(
                    email=temp_user.email,
                    password=temp_user.password,
                    # name=temp_user.name,
                    is_active=True,
                    is_email_verified=True
                )
                # user.assign_role(temp_user.role)
                logger.info(f"Created new user: {user.email}")

            temp_user.delete()
            logger.info(f"Deleted temporary user: {temp_user.email}")
            
            # Authenticate the user
            authenticated_user = authenticate(request, username=user.email, password=temp_user.password)
            if authenticated_user is not None:
                login(request, authenticated_user)
            else:
                logger.error(f"Failed to authenticate user: {user.email}")
            refresh = RefreshToken.for_user(user)
            return render(request, 'account/verify_success.html', {
                # 'user_name': user.name,
                'user_email': user.email,
                # "role": user.role.name if user.role else None,
                'refresh_token': str(refresh),
                'access_token': str(refresh.access_token),
            })
        except TemporaryUser.DoesNotExist:
            logger.error(f"No temporary user found with token: {token}")
            # Check if there's a verified user with this email
            email = request.GET.get('email')  # Assuming email is passed as a query parameter
            email = request.GET.get('email')
            if email:
                user = User.objects.filter(email=email, is_email_verified=True).first()
                if user:
                    return render(request, 'account/already_verified.html', {
                        'message': 'This email is already verified.'
                    })

            return render(request, 'account/already_verified.html', {
                'message': 'Invalid verification token'
            })
        except Exception as e:
            logger.exception(f"Unexpected error during email verification: {str(e)}")
            return render(request, 'account/verify_failed.html', {
                'message': 'An unexpected error occurred'
            })



# Add this new view
from django.shortcuts import redirect
from rest_framework_simplejwt.tokens import RefreshToken
class AutoLoginView(APIView):
    permission_classes = [AllowAny]
    def get(self, request):
        access_token = request.GET.get('access_token')
        refresh_token = request.GET.get('refresh_token')
        
        site_url = settings.SITE_URL
        
        if access_token and refresh_token:
            # Here you would typically validate the tokens
            # For simplicity, we're assuming they're valid
            # Redirect to the main application with the tokens
            return redirect(f'{site_url}/verify?access_token={access_token}&refresh_token={refresh_token}')
        else:
            return Response({"error": "Invalid tokens"}, status=status.HTTP_400_BAD_REQUEST)




class LoginAPI(APIView):
    permission_classes = [AllowAny]
    renderer_classes = [JSONRenderer]  # Ensure JSON rendering

    def post(self, request):
        serializer = LoginSerializer(data=request.data, context={"request": request})
        if serializer.is_valid():
            user = serializer.validated_data["user"]
            tokens = get_tokens_for_user(user)
            response_data = {
                "status": 200,
                "message": "Login successful",
                "user_id": user.id,  # Include the user ID here
                "email": user.email,
                # "role": user.role.name if user.role else None,
                "refresh_token": tokens["refresh"],
                "access_token": tokens["access"],
            }
            return Response(response_data, status=status.HTTP_200_OK)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)






class LogoutView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            # Get the current user
            user = request.user

            # Create a refresh token for the current user
            refresh_token = RefreshToken.for_user(user)

            # Blacklist the refresh token to log out the user
            refresh_token.blacklist()

            return Response(
                {"message": "Logged out successfully"},
                status=status.HTTP_205_RESET_CONTENT,
            )
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


# class ProfileViewSet(viewsets.ModelViewSet):
#     queryset = Profile.objects.all()
#     serializer_class = ProfileSerializer
#     permission_classes = [IsAuthenticated]

#     def perform_create(self, serializer):
#         serializer.save(user=self.request.user)

#     def perform_update(self, serializer):
#         serializer.save(user=self.request.user)




# class CommunityViewSet(viewsets.ModelViewSet):
#     queryset = Community.objects.all()
#     serializer_class = CommunitySerializer

#     def perform_create(self, serializer):
#         serializer.save()

#     def join_community(self, request, pk=None):
#         community = self.get_object()
#         community.members.add(request.user)
#         return Response(status=status.HTTP_204_NO_CONTENT)

#     def leave_community(self, request, pk=None):
#         community = self.get_object()
#         community.members.remove(request.user)
#         return Response(status=status.HTTP_204_NO_CONTENT)

#     @action(detail=False, methods=['get'], url_path='search')
#     def search_communities(self, request):
#         query = request.query_params.get("q", "")
#         if query:
#             # Combine exact, case-insensitive, and similarity searches
#             communities = Community.objects.annotate(
#                 similarity=TrigramSimilarity('name', query) + TrigramSimilarity('description', query)
#             ).filter(
#                 Q(name__iexact=query) |  # Exact match (case-insensitive)
#                 Q(name__icontains=query) |  # Partial match (case-insensitive)
#                 Q(description__icontains=query) |  # Partial match in description
#                 Q(similarity__gt=0.3)  # Similar names or descriptions
#             ).order_by('-similarity', 'name')  # Order by similarity, then name

#             serializer = self.get_serializer(communities, many=True)
#             return Response(serializer.data)
#         return Response({"detail": "Please provide a search query."}, status=status.HTTP_400_BAD_REQUEST)

class PasswordResetView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        serializer = PasswordResetSerializer(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data['email']
            user = CustomUser.objects.get(email=email)
            
            # Generate password reset token
            token = default_token_generator.make_token(user)
            uid = urlsafe_base64_encode(force_bytes(user.pk))
            
            # Create password reset link
            reset_link = request.build_absolute_uri(
                reverse('password_reset_confirm', kwargs={'uidb64': uid, 'token': token})
            )
            
            # Send email with reset link
            subject = "Password Reset for R.A.V.I.D."
            html_content = render_to_string('account/password_reset.html', {
                'user': user,
                'reset_link': reset_link,
            })
            plain_content = strip_tags(html_content)
            
            try:
                send_mail(
                    subject=subject,
                    message=plain_content,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[user.email],
                    html_message=html_content,
                    fail_silently=False,
                )
                return Response({"message": "Password reset email sent"}, status=status.HTTP_200_OK)
            except Exception as e:
                return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class PasswordResetConfirmView(APIView):
    permission_classes = [AllowAny]

    def get(self, request, uidb64, token):
        # Render a page with a form to enter the new password
        return render(request, 'account/password_reset_confirm.html', {
            'uidb64': uidb64,
            'token': token
        })

    def post(self, request, uidb64, token):
        serializer = PasswordResetConfirmSerializer(data=request.data)
        if serializer.is_valid():
            try:
                uid = urlsafe_base64_decode(uidb64).decode()
                user = CustomUser.objects.get(pk=uid)
            except (TypeError, ValueError, OverflowError, CustomUser.DoesNotExist):
                return render(request, 'account/password_reset_confirm.html', {
                    'error': "Invalid reset link"
                })

            if default_token_generator.check_token(user, token):
                new_password = serializer.validated_data['new_password']
                user.set_password(new_password)
                user.save()
                # Redirect to success page
                return render(request, 'account/password_reset_success.html', {
                    'email': user.email,
                    'home_url': settings.SITE_URL
                })
            else:
                return render(request, 'account/password_reset_confirm.html', {
                    'error': "Invalid reset link"
                })
        
        return render(request, 'account/password_reset_confirm.html', {
            'error': serializer.errors
        })
    
    
 
# class templateview(View):
#     def get(self, request):
#         return render(request, 'roles/credential_submission.html')   

class EmailChangeView(APIView):
    permission_classes = [IsAuthenticated]
    serializer_class = EmailChangeSerializer

    def post(self, request):
        # Validate input with serializer
        serializer = EmailChangeSerializer(data=request.data, context={"request": request})
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        user = request.user
        new_email = serializer.validated_data["new_email"]
        old_email = user.email
        
        # Create or update PendingEmailChange
        pending_change = PendingEmailChange.objects.filter(user=user, new_email=new_email).first()
        if pending_change:
            pending_change.created_at = timezone.now()
            pending_change.token = uuid4()
            pending_change.save()
        else:
            pending_change = PendingEmailChange.objects.create(
                user=user,
                new_email=new_email, 
                token=uuid4(),
                created_at=timezone.now(),
            )
        
        # Send verification email to new email address
        verification_link = request.build_absolute_uri(
            reverse('verify_email_change', kwargs={'token': pending_change.token})
        )
        self._send_verification_email(user, new_email, verification_link)
        
        # Send notification to old email address
        self._send_notification_email(user, old_email, new_email)
        
        return Response({
            "message": "Verification email sent. Please check both your current and new email addresses.",
            "old_email": old_email,
            "new_email": new_email
        }, status=status.HTTP_200_OK)

    def _send_verification_email(self, user, new_email, verification_link):
        subject = "Confirm your new email address"
        html_content = render_to_string('account/email_change_verification.html', {
            'user': user,
            'verification_link': verification_link,
            'new_email': new_email,
            'expiry_hours': 24  # Add expiry time if implemented
        })
        plain_content = strip_tags(html_content)
        
        try:
            send_mail(
                subject=subject,
                message=plain_content,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[new_email],
                html_message=html_content,
                fail_silently=False,
            )
        except Exception as e:
            logger.error(f"Failed to send verification email: {str(e)}")
            raise serializers.ValidationError("Failed to send verification email")

    def _send_notification_email(self, user, old_email, new_email):
        subject = "Email Change Request Initiated"
        html_content = render_to_string('account/email_change_notification.html', {
            'user': user,
            'new_email': new_email,
        })
        plain_content = strip_tags(html_content)
        
        try:
            send_mail(
                subject=subject,
                message=plain_content,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[old_email],
                html_message=html_content,
                fail_silently=False,
            )
        except Exception as e:
            logger.error(f"Failed to send notification email: {str(e)}")
            # Continue with the process even if notification fails

class VerifyEmailChangeView(APIView):
    permission_classes = [AllowAny]

    def get(self, request, token):
        try:
            pending_change = PendingEmailChange.objects.get(token=token)
            
            # Check if token is expired
            if pending_change.is_expired():
                # Optionally delete expired tokens
                pending_change.delete()
                return Response({
                    "error": "Verification link has expired"
                }, status=status.HTTP_400_BAD_REQUEST)
            
            user = pending_change.user
            new_email = pending_change.new_email
            
            # Update user's email
            user.email = new_email
            user.save()
            
            # Delete the pending change 
            pending_change.delete()
            
            return render(request, 'account/email_changed_success.html', {
                'level': 'Primary',
                'home_url': settings.SITE_URL
            })
           
        except PendingEmailChange.DoesNotExist:
            return render(request, '404.html', {
                'exception': "Email verification link is invalid"
            })
        except Exception as e:
            logger.error(f"Error during second email verification: {str(e)}")
            return render(request, '500.html')
class AccountDeletionView(APIView):
    """View for handling user account deletion requests.

    Requires authentication. Deletes the user account and all associated data,
    including social accounts and temporary user data.
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            user = request.user

            # Start a transaction to ensure atomic deletion
            with transaction.atomic():
                # Delete all related social accounts
                SocialAccount.objects.filter(user=user).delete()

                # Delete any temporary user data if exists
                TemporaryUser.objects.filter(email=user.email).delete()

                # Delete the user (this will cascade to all related models due to on_delete=CASCADE)
                user.delete()

                # Delete the user from backup database
                # TODO: Implement this

                # Disconnect the user from the oauth provider if connected
                # TODO: Implement this

            return Response({"message": "Account and all associated data deleted successfully"},
                            status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(f"Error deleting account for user {
                         request.user.email}: {str(e)}")
            return Response({"error": "Failed to delete account"},
                            status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
class ChangeSecondEmailView(APIView):
    permission_classes = [IsAuthenticated]
    
    
    def post(self, request):
        new_second_email = request.data.get('second_email')
        if not new_second_email:
            return Response({
                "error": "second_email is required"
            }, status=status.HTTP_400_BAD_REQUEST)
            
        # Validate email format
        try:
            validate_email(new_second_email)
        except ValidationError:
            return Response({
                "error": "Invalid email format"
            }, status=status.HTTP_400_BAD_REQUEST)
            
        user = request.user
        
        # Create or update PendingEmailChange for second email
        pending_change = PendingEmailChange.objects.filter(
            user=user, 
            new_email=new_second_email
        ).first()
        
        if pending_change:
            pending_change.created_at = timezone.now()
            pending_change.token = uuid4()
            pending_change.save()
        else:
            pending_change = PendingEmailChange.objects.create(
                user=user,
                new_email=new_second_email,
                token=uuid4(),
                created_at=timezone.now()
            )
        
        # Send verification email
        verification_link = request.build_absolute_uri(
            reverse('verify_second_email', kwargs={'token': pending_change.token})
        )
        
        try:
            subject = "Verify your secondary email address"
            html_content = render_to_string('account/second_email_verification.html', {
                'user': user,
                'verification_link': verification_link,
                'new_email': new_second_email,
                'expiry_hours': 24
            })
            plain_content = strip_tags(html_content)
            
            send_mail(
                subject=subject,
                message=plain_content,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[new_second_email],
                html_message=html_content,
                fail_silently=False,
            )
            
            return Response({
                "message": "Verification email sent to your secondary email address.",
                "second_email": new_second_email
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Failed to send verification email: {str(e)}")
            return Response({
                "error": "Failed to send verification email"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class VerifySecondEmailView(APIView):
    permission_classes = [AllowAny]

    def get(self, request, token):
        try:
            pending_change = PendingEmailChange.objects.get(token=token)
            
            # Check if token is expired
            if pending_change.is_expired():
                pending_change.delete()
                return Response({
                    "error": "Verification link has expired"
                }, status=status.HTTP_400_BAD_REQUEST)
            
            user = pending_change.user
            new_second_email = pending_change.new_email
            
            # Update user's second email
            user.second_email = new_second_email
            user.save()
            
            # Delete the pending change
            pending_change.delete()
            
            return render(request, 'account/email_changed_success.html', {
                'level': 'Secondary',
                'home_url': settings.SITE_URL
            })
            
        except PendingEmailChange.DoesNotExist:
            return render(request, '404.html', {
                'exception': "Email verification link is invalid"
            })
        except Exception as e:
            logger.error(f"Error during second email verification: {str(e)}")
            return render(request, '500.html')
        

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def check_custom_url_availability(request):
    """Check if custom URL username is available"""
    custom_url_username = request.data.get('custom_url_username', '').strip()
    
    if not custom_url_username:
        return Response({
            'error': 'Custom URL username is required'
        }, status=status.HTTP_400_BAD_REQUEST)
    
    is_valid, message = validate_custom_url_username(custom_url_username, exclude_user=request.user)
    
    return Response({
        'available': is_valid,
        'message': message,
        'suggested': generate_custom_url_suggestions(custom_url_username) if not is_valid else []
    })

@api_view(['GET'])
@permission_classes([AllowAny])
def get_profile_by_identifier(request, identifier):
    """Get user profile by custom_url_username or ID"""
    user = get_user_by_identifier_or_404(identifier)
    
    # Import here to avoid circular imports
    from roles.serializers.profile import UserProfileSerializer
    
    # Return different data based on privacy settings
    if request.user.is_authenticated and request.user == user:
        # Own profile - full data
        profile = getattr(user, 'profile', None)
        if not profile:
            return Response({'error': 'Profile not found'}, status=status.HTTP_404_NOT_FOUND)
        serializer = UserProfileSerializer(profile)
    else:
        # Public profile - check if profile is public
        profile = getattr(user, 'profile', None)
        if not profile:
            return Response({'error': 'Profile not found'}, status=status.HTTP_404_NOT_FOUND)
            
        if not getattr(profile, 'is_public_profile', False):
            return Response({'error': 'Profile is private'}, status=status.HTTP_403_FORBIDDEN)
        
        # Return limited public data
        serializer = UserProfileSerializer(profile)
        data = serializer.data
        
        # Remove sensitive fields for public view
        sensitive_fields = ['email', 'second_email', 'phone_number', 'homephone', 
                          'address', 'zipcode', 'is_email_verified', 'is_phone_verified']
        for field in sensitive_fields:
            data.pop(field, None)
            
        return Response(data)
    
    return Response(serializer.data)


